﻿#pragma checksum "..\..\..\..\Views\PrintSettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CF30D3FCED85D09A438D98BC020CD85F5F28F8D8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// PrintSettingsWindow
    /// </summary>
    public partial class PrintSettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 37 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PrinterComboBox;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PrinterStatusText;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaperSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton PortraitRadio;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton LandscapeRadio;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox QualityComboBox;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaperTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CopiesTextBox;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton AllPagesRadio;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton PageRangeRadio;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FromPageTextBox;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ToPageTextBox;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton ColorRadio;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton BlackWhiteRadio;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox DuplexCheckBox;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TopMarginTextBox;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RightMarginTextBox;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BottomMarginTextBox;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LeftMarginTextBox;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewButton;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Views\PrintSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/printsettingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\PrintSettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PrinterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 38 "..\..\..\..\Views\PrintSettingsWindow.xaml"
            this.PrinterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PrinterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.PrinterStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.PaperSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.PortraitRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 5:
            this.LandscapeRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 6:
            this.QualityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.PaperTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.CopiesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.AllPagesRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 10:
            this.PageRangeRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 11:
            this.FromPageTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.ToPageTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.ColorRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 14:
            this.BlackWhiteRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 15:
            this.DuplexCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.TopMarginTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.RightMarginTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.BottomMarginTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.LeftMarginTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            
            #line 195 "..\..\..\..\Views\PrintSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetMargin1_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 197 "..\..\..\..\Views\PrintSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetMargin2_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 199 "..\..\..\..\Views\PrintSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetNarrowMargins_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 201 "..\..\..\..\Views\PrintSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetNormalMargins_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 203 "..\..\..\..\Views\PrintSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetWideMargins_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.PreviewButton = ((System.Windows.Controls.Button)(target));
            
            #line 215 "..\..\..\..\Views\PrintSettingsWindow.xaml"
            this.PreviewButton.Click += new System.Windows.RoutedEventHandler(this.PreviewButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 218 "..\..\..\..\Views\PrintSettingsWindow.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.SaveSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 221 "..\..\..\..\Views\PrintSettingsWindow.xaml"
            this.SaveSettingsButton.Click += new System.Windows.RoutedEventHandler(this.SaveSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 224 "..\..\..\..\Views\PrintSettingsWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

