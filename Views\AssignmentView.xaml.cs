using System;
using System.ComponentModel;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;

namespace SFDSystem.Views
{
    /// <summary>
    /// Interaction logic for AssignmentView.xaml
    /// </summary>
    public partial class AssignmentView : UserControl, INotifyPropertyChanged
    {
        private string _currentDate;
        private string _currentHijriDate;

        public event PropertyChangedEventHandler PropertyChanged;

        public string CurrentDate
        {
            get => _currentDate;
            set
            {
                _currentDate = value;
                OnPropertyChanged(nameof(CurrentDate));
            }
        }

        public string CurrentHijriDate
        {
            get => _currentHijriDate;
            set
            {
                _currentHijriDate = value;
                OnPropertyChanged(nameof(CurrentHijriDate));
            }
        }

        public AssignmentView()
        {
            InitializeComponent();

            // تحديث التواريخ
            UpdateDates();

            // إنشاء ViewModel افتراضي فقط إذا لم يكن هناك DataContext
            if (DataContext == null)
            {
                DataContext = new ReportViewModel();
            }
        }

        /// <summary>
        /// Constructor مع DataContext محدد مسبقاً
        /// </summary>
        public AssignmentView(ReportViewModel viewModel)
        {
            InitializeComponent();

            // تحديث التواريخ
            UpdateDates();

            DataContext = viewModel;
        }

        private void UpdateDates()
        {
            var now = DateTime.Now;
            CurrentDate = now.ToString("yyyy/MM/dd");
            CurrentHijriDate = HijriDateService.ConvertToHijri(now);
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }


    }
}
