<UserControl x:Class="DriverManagementSystem.Views.AssignmentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             FlowDirection="LeftToRight"
             Background="White">


    <Border BorderBrush="#444" BorderThickness="2" CornerRadius="0" Padding="25">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>

            <!-- Row 0: الهيدر - الفوتر العلوي -->
            <Grid Grid.Row="0" Background="#F0F0F0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="هاتف: 8009800" FontFamily="Segoe UI" FontSize="13" FontWeight="Bold"
                           VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="#2C3E50"/>
                <TextBlock Grid.Column="1" Text="ص.ب: 15" FontFamily="Segoe UI" FontSize="13"
                           VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="#2C3E50"/>
                <TextBlock Grid.Column="2" Text="فاكس: 06-508301" FontFamily="Segoe UI" FontSize="13"
                           VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="#2C3E50"/>
            </Grid>

            <!-- Row 1: الرأس الرسمي -->
            <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,20,0,20">
                <!-- يسار: الشعار -->
                <Image Width="55" Height="55" Margin="0,0,20,0" VerticalAlignment="Top">
                    <!-- LogoPlaceholder -->
                </Image>

                <!-- وسط: النصوص الرسمية -->
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Margin="50,0,50,0">
                    <TextBlock Text="الجمهورية اليمنية" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" Foreground="#2C3E50" Margin="0,2"/>
                    <TextBlock Text="Presidency of Council of Ministers" FontFamily="Segoe UI" FontSize="14"
                               HorizontalAlignment="Center" Foreground="#2C3E50" Margin="0,2"/>
                    <TextBlock Text="الصندوق الاجتماعي للتنمية" FontFamily="Segoe UI" FontSize="14"
                               HorizontalAlignment="Center" Foreground="#2C3E50" Margin="0,2"/>
                    <TextBlock Text="فرع ذمار البيضاء - Dhamar &amp; Albidaa Branch" FontFamily="Segoe UI" FontSize="14"
                               HorizontalAlignment="Center" Foreground="#2C3E50" Margin="0,2"/>
                </StackPanel>

                <!-- يمين: التواريخ -->
                <StackPanel VerticalAlignment="Top" HorizontalAlignment="Right" Margin="20,0,0,0">
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="التاريخ: " FontFamily="Segoe UI" FontSize="14" FontWeight="Bold" Foreground="#2C3E50"/>
                        <TextBlock Text="{Binding CurrentDate, FallbackValue='2025/01/07'}" FontFamily="Segoe UI" FontSize="14" Foreground="#2C3E50"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="الموافق: " FontFamily="Segoe UI" FontSize="14" FontWeight="Bold" Foreground="#2C3E50"/>
                        <TextBlock Text="{Binding CurrentHijriDate, FallbackValue='07/رجب/1446هـ'}" FontFamily="Segoe UI" FontSize="14" Foreground="#2C3E50"/>
                    </StackPanel>
                </StackPanel>
            </StackPanel>

            <!-- Row 2: جسم الوثيقة -->
            <StackPanel Grid.Row="2" Margin="0,10,0,20">

                <!-- عنوان تكليف -->
                <TextBlock Text="تكليف" FontFamily="Segoe UI" FontSize="28" FontWeight="Bold"
                           HorizontalAlignment="Center" Foreground="#2C3E50" Margin="0,15"/>

                <!-- فقرة التكليف الرئيسة -->
                <TextBlock TextWrapping="Wrap" FontFamily="Segoe UI" FontSize="14" Foreground="#2C3E50"
                           Width="90%" HorizontalAlignment="Center" Margin="0,15" TextAlignment="Justify">
                    <Run Text="يكلف الصندوق الاجتماعي للتنمية - فرع ذمار البيضاء الأخوة المبين أسماؤهم في الجدول أدناه لتنفيذ المهمة التالية:-"/>
                </TextBlock>

                <!-- حقول المشروع / النشاط / خط السير -->
                <UniformGrid Columns="3" Margin="0,10">
                    <Border BorderBrush="#999" BorderThickness="1" Margin="2">
                        <StackPanel Padding="5">
                            <TextBlock Text="المشروع" FontFamily="Segoe UI" FontWeight="Bold" FontSize="14"
                                       TextAlignment="Center" Foreground="#2C3E50" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding ProjectName, FallbackValue=''}" FontFamily="Segoe UI" FontSize="12"
                                       TextAlignment="Center" Foreground="#2C3E50" MinHeight="20"/>
                        </StackPanel>
                    </Border>
                    <Border BorderBrush="#999" BorderThickness="1" Margin="2">
                        <StackPanel Padding="5">
                            <TextBlock Text="النشاط" FontFamily="Segoe UI" FontWeight="Bold" FontSize="14"
                                       TextAlignment="Center" Foreground="#2C3E50" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding Activity, FallbackValue=''}" FontFamily="Segoe UI" FontSize="12"
                                       TextAlignment="Center" Foreground="#2C3E50" MinHeight="20"/>
                        </StackPanel>
                    </Border>
                    <Border BorderBrush="#999" BorderThickness="1" Margin="2">
                        <StackPanel Padding="5">
                            <TextBlock Text="خط السير" FontFamily="Segoe UI" FontWeight="Bold" FontSize="14"
                                       TextAlignment="Center" Foreground="#2C3E50" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding Route, FallbackValue=''}" FontFamily="Segoe UI" FontSize="12"
                                       TextAlignment="Center" Foreground="#2C3E50" MinHeight="20"/>
                        </StackPanel>
                    </Border>
                </UniformGrid>

        <!-- جدول المكلفين -->
        <StackPanel Grid.Row="4" Margin="0,0,0,20">
            <TextBlock Text="بيان المكلفين:" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                       Foreground="#000000" Margin="0,0,0,10"/>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="40"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- رؤوس الأعمدة -->
                <Border Grid.Row="0" Grid.Column="0" Background="#F0F0F0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="م" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="1" Background="#F0F0F0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="الاسم" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="2" Background="#F0F0F0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="رقم الهوية" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="3" Background="#F0F0F0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="نوع الهوية" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="4" Background="#F0F0F0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="رقم الجوال" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="5" Background="#F0F0F0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="الصفة" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="6" Background="#F0F0F0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="بيان المهمة" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                </Border>

                <!-- صفوف البيانات -->
                <Border Grid.Row="1" Grid.Column="0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="1" FontFamily="Segoe UI" FontSize="14"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="1" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="2" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="3" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="4" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="5" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="6" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>

                <!-- صف ثاني -->
                <Border Grid.Row="2" Grid.Column="0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="2" FontFamily="Segoe UI" FontSize="14"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="1" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="2" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="3" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="4" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="5" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="6" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>

                <!-- صف ثالث -->
                <Border Grid.Row="3" Grid.Column="0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="3" FontFamily="Segoe UI" FontSize="14"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="3" Grid.Column="1" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="3" Grid.Column="2" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="3" Grid.Column="3" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="3" Grid.Column="4" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="3" Grid.Column="5" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="3" Grid.Column="6" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
            </Grid>
        </StackPanel>

        <!-- جدول وسائل النقل -->
        <StackPanel Grid.Row="5" Margin="0,0,0,20">
            <TextBlock Text="وسائل النقل:" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                       Foreground="#000000" Margin="0,0,0,10"/>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="40"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="120"/>
                </Grid.ColumnDefinitions>

                <!-- رؤوس الأعمدة -->
                <Border Grid.Row="0" Grid.Column="0" Background="#F0F0F0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="م" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="1" Background="#F0F0F0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="اسم السائق" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="2" Background="#F0F0F0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="رقم اللوحة" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="3" Background="#F0F0F0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="نوع السيارة" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="4" Background="#F0F0F0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="رقم الهاتف" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="5" Background="#F0F0F0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="رقم الهوية" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5"/>
                </Border>

                <!-- صفوف البيانات -->
                <Border Grid.Row="1" Grid.Column="0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="1" FontFamily="Segoe UI" FontSize="14"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="1" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="2" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="3" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="4" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="5" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>

                <!-- صف ثاني -->
                <Border Grid.Row="2" Grid.Column="0" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock Text="2" FontFamily="Segoe UI" FontSize="14"
                               HorizontalAlignment="Center" VerticalAlignment="Center" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="1" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="2" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="3" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="4" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="5" BorderBrush="#B0B0B0" BorderThickness="1">
                    <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="30"/>
                </Border>
            </Grid>
        </StackPanel>

        <!-- فقرة الختام -->
        <TextBlock Grid.Row="6" TextWrapping="Wrap" FontFamily="Segoe UI" FontSize="14"
                   Foreground="#2C3E50" Margin="0,20,0,20" TextAlignment="Center">
            <Run Text="لما فيه المصلحة العامة"/>
            <LineBreak/>
            <Run Text="،،، والله الموفق"/>
        </TextBlock>

        <!-- التوقيع -->
        <Grid Grid.Row="7" Margin="0,30,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="200"/>
            </Grid.ColumnDefinitions>

            <!-- مساحة فارغة -->
            <Border Grid.Column="0"/>

            <!-- بيانات المدير -->
            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                <TextBlock Text="مدير الفرع" FontFamily="Segoe UI" FontSize="14"
                           Foreground="#2C3E50" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                <TextBlock Text="م/محمد محمد الديلمي" FontFamily="Segoe UI" FontSize="14" FontWeight="Bold"
                           Foreground="#000000" HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
