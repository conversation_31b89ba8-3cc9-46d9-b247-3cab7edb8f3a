<Window x:Class="SFDSystem.Views.AssignmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:views="clr-namespace:SFDSystem.Views"
        Title="التكليف - نظام إدارة الزيارات الميدانية"
        Height="700"
        Width="950"
        MinHeight="600"
        MinWidth="800"
        WindowStartupLocation="CenterScreen"
        WindowState="Normal"
        ResizeMode="CanResize"
        Background="#F5F5F5"
        FlowDirection="RightToLeft"
        FontFamily="Arial">

    <!-- الشريط العلوي الأزرق -->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header الأزرق -->
        <Border Grid.Row="0" Background="#2196F3">
            <Grid Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- أزرار اليسار -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Name="CloseButton" Content="✖️ إغلاق"
                        Background="#F44336" Foreground="White"
                        BorderThickness="0" Padding="15,8" Margin="5,0"
                        FontWeight="Bold" Cursor="Hand"
                        Click="CloseButton_Click"/>
                    <Button Name="PrintButton" Content="🖨️ طباعة"
                        Background="#4CAF50" Foreground="White"
                        BorderThickness="0" Padding="15,8" Margin="5,0"
                        FontWeight="Bold" Cursor="Hand"
                        Click="PrintButton_Click"/>
                </StackPanel>

                <!-- العنوان في الوسط -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="📋 تقرير النزول الميدانية"
                           FontSize="18" FontWeight="Bold" Foreground="White" Width="695"
                           />
                </StackPanel>

                <!-- رقم الزيارة على اليمين -->
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- الشريط الجانبي الأيسر -->
            <Border Grid.Column="0" Background="White" BorderBrush="#DEE2E6" BorderThickness="0,0,1,0" Margin="20,20,0,20">
                <StackPanel Margin="15">
                    <!-- رقم الزيارة فقط -->
                    <Border Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="2"
                            Padding="20" Margin="0,50,0,0" HorizontalAlignment="Center">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="رقم الزيارة" FontSize="16" FontWeight="Bold"
                                       HorizontalAlignment="Center" TextAlignment="Center" Margin="0,0,0,10"/>
                            <TextBlock Text="{Binding SelectedVisit.VisitNumber, FallbackValue='911-1300'}"
                                       FontSize="24" FontWeight="Bold"
                                       HorizontalAlignment="Center" TextAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Border>

            <!-- منطقة المحتوى الرئيسي -->
            <Grid Grid.Column="1" Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- أزرار الطباعة -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,0,0,20"/>

                <!-- منطقة المحتوى -->
                <Border Grid.Row="1" Background="White" BorderBrush="#DEE2E6" BorderThickness="1">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                        <views:AssignmentView x:Name="AssignmentContent" Loaded="AssignmentContent_Loaded"/>
                    </ScrollViewer>
                </Border>
            </Grid>
        </Grid>
    </Grid>

    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                            CornerRadius="5"
                            Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
</Window>
