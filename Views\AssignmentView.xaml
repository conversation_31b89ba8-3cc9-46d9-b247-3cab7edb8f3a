<UserControl x:Class="SFDSystem.Views.AssignmentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:SFDSystem.Views"
             FlowDirection="LeftToRight"
             Background="White">


    <Border BorderBrush="#444" BorderThickness="2" CornerRadius="0" Padding="25">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>

            <!-- Row 0: الهيدر - الفوتر العلوي -->
            <Grid Grid.Row="0" Background="#F0F0F0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="هاتف: 8009800" FontFamily="Segoe UI" FontSize="13" FontWeight="Bold"
                           VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="#2C3E50"/>
                <TextBlock Grid.Column="1" Text="ص.ب: 15" FontFamily="Segoe UI" FontSize="13"
                           VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="#2C3E50"/>
                <TextBlock Grid.Column="2" Text="فاكس: 06-508301" FontFamily="Segoe UI" FontSize="13"
                           VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="#2C3E50"/>
            </Grid>

            <!-- Row 1: الرأس الرسمي -->
            <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,20,0,20">
                <!-- يسار: الشعار -->
                <Image Width="55" Height="55" Margin="0,0,20,0" VerticalAlignment="Top">
                    <!-- LogoPlaceholder -->
                </Image>

                <!-- وسط: النصوص الرسمية -->
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Margin="50,0,50,0">
                    <TextBlock Text="الجمهورية اليمنية" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" Foreground="#2C3E50" Margin="0,2"/>
                    <TextBlock Text="Presidency of Council of Ministers" FontFamily="Segoe UI" FontSize="14"
                               HorizontalAlignment="Center" Foreground="#2C3E50" Margin="0,2"/>
                    <TextBlock Text="الصندوق الاجتماعي للتنمية" FontFamily="Segoe UI" FontSize="14"
                               HorizontalAlignment="Center" Foreground="#2C3E50" Margin="0,2"/>
                    <TextBlock Text="فرع ذمار البيضاء - Dhamar &amp; Albidaa Branch" FontFamily="Segoe UI" FontSize="14"
                               HorizontalAlignment="Center" Foreground="#2C3E50" Margin="0,2"/>
                </StackPanel>

                <!-- يمين: التواريخ -->
                <StackPanel VerticalAlignment="Top" HorizontalAlignment="Right" Margin="20,0,0,0">
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="التاريخ: " FontFamily="Segoe UI" FontSize="14" FontWeight="Bold" Foreground="#2C3E50"/>
                        <TextBlock Text="{Binding CurrentDate, FallbackValue='2025/01/07'}" FontFamily="Segoe UI" FontSize="14" Foreground="#2C3E50"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="الموافق: " FontFamily="Segoe UI" FontSize="14" FontWeight="Bold" Foreground="#2C3E50"/>
                        <TextBlock Text="{Binding CurrentHijriDate, FallbackValue='07/رجب/1446هـ'}" FontFamily="Segoe UI" FontSize="14" Foreground="#2C3E50"/>
                    </StackPanel>
                </StackPanel>
            </StackPanel>

            <!-- Row 2: جسم الوثيقة -->
            <StackPanel Grid.Row="2" Margin="0,10,0,20">

                <!-- عنوان تكليف -->
                <TextBlock Text="تكليف" FontFamily="Segoe UI" FontSize="28" FontWeight="Bold"
                           HorizontalAlignment="Center" Foreground="#2C3E50" Margin="0,15"/>

                <!-- فقرة التكليف الرئيسة -->
                <TextBlock TextWrapping="Wrap" FontFamily="Segoe UI" FontSize="14" Foreground="#2C3E50"
                           Width="90%" HorizontalAlignment="Center" Margin="0,15" TextAlignment="Justify">
                    <Run Text="يكلف الصندوق الاجتماعي للتنمية - فرع ذمار البيضاء الأخوة المبين أسماؤهم في الجدول أدناه لتنفيذ المهمة التالية:-"/>
                </TextBlock>

                <!-- حقول المشروع / النشاط / خط السير -->
                <UniformGrid Columns="3" Margin="0,10">
                    <Border BorderBrush="#999" BorderThickness="1" Margin="2">
                        <StackPanel Margin="5">
                            <TextBlock Text="المشروع" FontFamily="Segoe UI" FontWeight="Bold" FontSize="14"
                                       TextAlignment="Center" Foreground="#2C3E50" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding ProjectName, FallbackValue=''}" FontFamily="Segoe UI" FontSize="12"
                                       TextAlignment="Center" Foreground="#2C3E50" MinHeight="20"/>
                        </StackPanel>
                    </Border>
                    <Border BorderBrush="#999" BorderThickness="1" Margin="2">
                        <StackPanel Margin="5">
                            <TextBlock Text="النشاط" FontFamily="Segoe UI" FontWeight="Bold" FontSize="14"
                                       TextAlignment="Center" Foreground="#2C3E50" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding Activity, FallbackValue=''}" FontFamily="Segoe UI" FontSize="12"
                                       TextAlignment="Center" Foreground="#2C3E50" MinHeight="20"/>
                        </StackPanel>
                    </Border>
                    <Border BorderBrush="#999" BorderThickness="1" Margin="2">
                        <StackPanel Margin="5">
                            <TextBlock Text="خط السير" FontFamily="Segoe UI" FontWeight="Bold" FontSize="14"
                                       TextAlignment="Center" Foreground="#2C3E50" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding Route, FallbackValue=''}" FontFamily="Segoe UI" FontSize="12"
                                       TextAlignment="Center" Foreground="#2C3E50" MinHeight="20"/>
                        </StackPanel>
                    </Border>
                </UniformGrid>

                <!-- جدول المكلفين -->
                <TextBlock Text="بيان المكلفين:" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                           Foreground="#2C3E50" Margin="0,20,0,10"/>

                <Grid Margin="0,5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="40"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- رؤوس الأعمدة -->
                    <Border Grid.Row="0" Grid.Column="0" Background="#E0E0E0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="م" FontFamily="Segoe UI" FontWeight="Bold" FontSize="16"
                                   TextAlignment="Center" Padding="5" Foreground="#2C3E50"/>
                    </Border>
                    <Border Grid.Row="0" Grid.Column="1" Background="#E0E0E0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="الاسم" FontFamily="Segoe UI" FontWeight="Bold" FontSize="16"
                                   TextAlignment="Center" Padding="5" Foreground="#2C3E50"/>
                    </Border>
                    <Border Grid.Row="0" Grid.Column="2" Background="#E0E0E0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="رقم الهوية" FontFamily="Segoe UI" FontWeight="Bold" FontSize="16"
                                   TextAlignment="Center" Padding="5" Foreground="#2C3E50"/>
                    </Border>
                    <Border Grid.Row="0" Grid.Column="3" Background="#E0E0E0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="نوع الهوية" FontFamily="Segoe UI" FontWeight="Bold" FontSize="16"
                                   TextAlignment="Center" Padding="5" Foreground="#2C3E50"/>
                    </Border>
                    <Border Grid.Row="0" Grid.Column="4" Background="#E0E0E0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="رقم الجوال" FontFamily="Segoe UI" FontWeight="Bold" FontSize="16"
                                   TextAlignment="Center" Padding="5" Foreground="#2C3E50"/>
                    </Border>
                    <Border Grid.Row="0" Grid.Column="5" Background="#E0E0E0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="الصفة" FontFamily="Segoe UI" FontWeight="Bold" FontSize="16"
                                   TextAlignment="Center" Padding="5" Foreground="#2C3E50"/>
                    </Border>
                    <Border Grid.Row="0" Grid.Column="6" Background="#E0E0E0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="بيان المهمة" FontFamily="Segoe UI" FontWeight="Bold" FontSize="16"
                                   TextAlignment="Center" Padding="5" Foreground="#2C3E50"/>
                    </Border>

                    <!-- صفوف البيانات -->
                    <Border Grid.Row="1" Grid.Column="0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="1" FontFamily="Segoe UI" FontSize="14" TextAlignment="Center" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="1" Grid.Column="1" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="1" Grid.Column="2" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="1" Grid.Column="3" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="1" Grid.Column="4" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="1" Grid.Column="5" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="1" Grid.Column="6" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>

                    <Border Grid.Row="2" Grid.Column="0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="2" FontFamily="Segoe UI" FontSize="14" TextAlignment="Center" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="2" Grid.Column="1" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="2" Grid.Column="2" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="2" Grid.Column="3" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="2" Grid.Column="4" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="2" Grid.Column="5" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="2" Grid.Column="6" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>

                    <Border Grid.Row="3" Grid.Column="0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="3" FontFamily="Segoe UI" FontSize="14" TextAlignment="Center" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="3" Grid.Column="1" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="3" Grid.Column="2" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="3" Grid.Column="3" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="3" Grid.Column="4" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="3" Grid.Column="5" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="3" Grid.Column="6" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                </Grid>

                <!-- جدول وسائل النقل -->
                <TextBlock Text="وسائل النقل:" FontFamily="Segoe UI" FontSize="16" FontWeight="Bold"
                           Foreground="#2C3E50" Margin="0,20,0,10"/>

                <Grid Margin="0,5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="40"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="120"/>
                    </Grid.ColumnDefinitions>

                    <!-- رؤوس الأعمدة -->
                    <Border Grid.Row="0" Grid.Column="0" Background="#E0E0E0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="م" FontFamily="Segoe UI" FontWeight="Bold" FontSize="16"
                                   TextAlignment="Center" Padding="5" Foreground="#2C3E50"/>
                    </Border>
                    <Border Grid.Row="0" Grid.Column="1" Background="#E0E0E0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="اسم السائق" FontFamily="Segoe UI" FontWeight="Bold" FontSize="16"
                                   TextAlignment="Center" Padding="5" Foreground="#2C3E50"/>
                    </Border>
                    <Border Grid.Row="0" Grid.Column="2" Background="#E0E0E0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="رقم اللوحة" FontFamily="Segoe UI" FontWeight="Bold" FontSize="16"
                                   TextAlignment="Center" Padding="5" Foreground="#2C3E50"/>
                    </Border>
                    <Border Grid.Row="0" Grid.Column="3" Background="#E0E0E0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="نوع السيارة" FontFamily="Segoe UI" FontWeight="Bold" FontSize="16"
                                   TextAlignment="Center" Padding="5" Foreground="#2C3E50"/>
                    </Border>
                    <Border Grid.Row="0" Grid.Column="4" Background="#E0E0E0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="رقم الهاتف" FontFamily="Segoe UI" FontWeight="Bold" FontSize="16"
                                   TextAlignment="Center" Padding="5" Foreground="#2C3E50"/>
                    </Border>
                    <Border Grid.Row="0" Grid.Column="5" Background="#E0E0E0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="رقم الهوية" FontFamily="Segoe UI" FontWeight="Bold" FontSize="16"
                                   TextAlignment="Center" Padding="5" Foreground="#2C3E50"/>
                    </Border>

                    <!-- صفوف البيانات -->
                    <Border Grid.Row="1" Grid.Column="0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="1" FontFamily="Segoe UI" FontSize="14" TextAlignment="Center" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="1" Grid.Column="1" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="1" Grid.Column="2" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="1" Grid.Column="3" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="1" Grid.Column="4" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="1" Grid.Column="5" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>

                    <Border Grid.Row="2" Grid.Column="0" BorderBrush="#999" BorderThickness="1">
                        <TextBlock Text="2" FontFamily="Segoe UI" FontSize="14" TextAlignment="Center" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="2" Grid.Column="1" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="2" Grid.Column="2" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="2" Grid.Column="3" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="2" Grid.Column="4" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                    <Border Grid.Row="2" Grid.Column="5" BorderBrush="#999" BorderThickness="1">
                        <TextBlock FontFamily="Segoe UI" FontSize="14" Padding="5" MinHeight="25"/>
                    </Border>
                </Grid>

                <!-- حقلا تاريخ التحرك وتاريخ العودة -->
                <Grid Margin="0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="تاريخ التحرك: " FontFamily="Segoe UI" FontSize="14" FontWeight="Bold"
                               VerticalAlignment="Center" Foreground="#2C3E50" Margin="0,0,10,0"/>
                    <Border Grid.Column="1" BorderBrush="#999" BorderThickness="0,0,0,1" Margin="0,0,20,0">
                        <TextBlock Text="{Binding DepartureDate, FallbackValue=''}" FontFamily="Segoe UI" FontSize="14"
                                   Padding="5" MinHeight="25" Foreground="#2C3E50"/>
                    </Border>

                    <TextBlock Grid.Column="2" Text="تاريخ العودة: " FontFamily="Segoe UI" FontSize="14" FontWeight="Bold"
                               VerticalAlignment="Center" Foreground="#2C3E50" Margin="0,0,10,0"/>
                    <Border Grid.Column="3" BorderBrush="#999" BorderThickness="0,0,0,1">
                        <TextBlock Text="{Binding ReturnDate, FallbackValue=''}" FontFamily="Segoe UI" FontSize="14"
                                   Padding="5" MinHeight="25" Foreground="#2C3E50"/>
                    </Border>
                </Grid>
            </StackPanel>

            <!-- Row 3: الخاتمة والتوقيع -->
            <StackPanel Grid.Row="3" Margin="0,20">
                <!-- فقرة الختام -->
                <TextBlock TextWrapping="Wrap" FontFamily="Segoe UI" FontSize="14" Foreground="#2C3E50"
                           HorizontalAlignment="Right" Margin="0,0,0,20">
                    <Run Text="لما فيه المصلحة العامة"/>
                    <LineBreak/>
                    <Run Text="،،، والله الموفق"/>
                </TextBlock>

                <!-- التوقيع -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="200"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="مدير الفرع" FontFamily="Segoe UI" FontSize="14"
                               HorizontalAlignment="Left" VerticalAlignment="Bottom" Foreground="#2C3E50"/>
                    <TextBlock Grid.Column="1" Text="م/محمد محمد الديلمي" FontFamily="Segoe UI" FontSize="14" FontWeight="Bold"
                               HorizontalAlignment="Right" VerticalAlignment="Bottom" Foreground="#2C3E50"/>
                </Grid>
            </StackPanel>

            <!-- Row 4: الفوتر السفلي -->
            <Grid Grid.Row="4" Background="#F0F0F0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="هاتف: 8009800" FontFamily="Segoe UI" FontSize="13" FontWeight="Bold"
                           VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="#2C3E50"/>
                <TextBlock Grid.Column="1" Text="ص.ب: 15" FontFamily="Segoe UI" FontSize="13"
                           VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="#2C3E50"/>
                <TextBlock Grid.Column="2" Text="فاكس: 06-508301" FontFamily="Segoe UI" FontSize="13"
                           VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="#2C3E50"/>
            </Grid>
        </Grid>
    </Border>
</UserControl>


